/* 页面布局 */
.render-page {
  /* position: fixed; inset: 0; 已经能确保元素占满视口，但保留width/height以增强兼容性 */
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 20px;
  box-sizing: border-box;
  background: var(--color-bg-page);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  color: var(--color-content-accent);
  position: fixed;
  inset: 0;
}

/* 标题栏 */
.title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 8px;
  width: 100%;
  margin-bottom: 20px;
}

.title-bar__left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logo {
  width: 78px;
  height: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  object-fit: contain;
}

.user-controls {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
}

/* 主要内容区域 */
.render-container {
  display: flex;
  flex: 1;
  gap: 20px;
  overflow: hidden;
  /* 显式设置padding和margin为0，确保在不同浏览器中行为一致 */
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  max-width: 100%;
  width: 100%;
}

/* 渲染窗口 */
.render-window {
  flex: 1;
  min-width: 0;
  background: var(--color-bg-primary);
  border-radius: var(--radius-lg);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.button-container {
  position: absolute;
  right: 20px;
  bottom: 20px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
}

.control-button {
  padding: 4px 8px;
  display: flex;
  align-items: center;
  gap: 6px;
  background: var(--color-bg-input);
  border-radius: var(--radius-full);
  cursor: pointer;
}

.control-button span {
  color: var(--color-content-regular);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.icon-wrapper {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-content-regular);
}

/* 属性面板 */
.property-panel {
  width: 220px;
  flex-shrink: 0;
  padding: 12px;
  background: var(--color-bg-primary);
  border-radius: var(--radius-lg);
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow-y: auto;
  overflow-x: hidden;
}

.panel-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 4px;
}

.section-header span {
  color: var(--color-content-regular);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.dropdown-wrapper,
.upload-button-wrapper,
.search-wrapper {
  width: 100%;
}

.bottom-buttons {
  margin-top: auto;
  display: flex;
  justify-content: center;
  gap: 8px;
  padding-top: 12px;
}

/* 材质网格 */
.materials-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  /* min-height: 0确保flex子项可以正确收缩，即使内容溢出也不会导致容器增长 */
  min-height: 0;
  /* 允许内容在必要时溢出容器 */
  overflow: visible;
}

.render-page .materials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
  gap: 8px;
  overflow-y: auto;
  /* padding和负margin组合用于创建内部间距而不影响整体尺寸 */
  padding: 4px;
  margin: -4px;
  align-content: flex-start;
  flex: 1;
  /* 确保网格可以在flex容器中正确收缩 */
  min-height: 0;
}

/* 系统预设材质网格 - 使用更大的网格单元 */
.render-page .materials-grid.preset-materials {
  grid-template-columns: repeat(60px);
  gap: 12px;
}

/* 已应用材质网格 - 保持原有尺寸 */
.render-page .editable-materials-grid .materials-grid {
  grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
  gap: 8px;
}

/* 渲染区域 */
.render-area {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-bg-primary);
  /* 移除不完整的背景设置，因为没有设置background-image */
  overflow: hidden;
  position: relative;
}

.render-area canvas {
  /* !important用于覆盖可能来自第三方库的canvas样式 */
  width: 100% !important;
  height: 100% !important;
  outline: none;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-content-mute);
  border-radius: 50%;
  /* 只改变顶部边框颜色，创建旋转效果 */
  border-top: 3px solid var(--color-brand);
  animation: spin 1s linear infinite;
}

.loading-text {
  color: var(--color-content-accent);
  font-size: var(--font-size-sm);
}

/* 定义旋转动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 自定义材质状态 */
.custom-material-disabled {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.disabled-message {
  color: var(--color-content-mute);
  font-size: var(--font-size-sm);
  font-style: italic;
}

/* 无材质状态 */
.no-materials {
  text-align: center;
  color: var(--color-content-mute);
  font-size: var(--font-size-sm);
  padding: 20px;
}