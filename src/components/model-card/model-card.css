/* 模型卡片组件样式 - 根据Figma设计稿精准还原 */
.model-card {
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border); /* 边框-常规 */
  border-radius: var(--radius-base);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  aspect-ratio: 1; /* 保持正方形比例 */
  display: flex;
  flex-direction: column;
}

.model-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.model-card:active {
  transform: translateY(-2px);
}

/* 图片容器 */
.model-card__image-container {
  position: relative;
  width: 100%;
  flex: 1;
  background: var(--color-bg-overlay);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.model-card__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.model-card:hover .model-card__image {
  transform: scale(1.05);
}

/* 占位符 */
.model-card__placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-bg-overlay);
  color: var(--color-content-secondary);
}

.model-card__placeholder.hidden {
  display: none;
}

.model-card__placeholder-icon {
  opacity: 0.5;
}

/* 悬浮遮罩 */
.model-card__overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--color-brand);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.model-card:hover .model-card__overlay {
  opacity: 1;
}

.model-card__overlay-text {
  color: var(--color-content-invert);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  line-height: 1;
}

/* 内容区域 */
.model-card__content {
  padding: 12px 16px 16px 16px;
  display: flex;
  flex-direction: column;
  gap: 4px; /* 设计稿间距 */
  background: var(--color-bg-primary);
}

.model-card__title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  color: var(--color-content-accent);
  margin: 0;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.model-card__meta {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: var(--font-size-base);
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  color: var(--color-content-regular);
  line-height: 1;
}

.model-card__file-type {
  background: var(--color-bg-overlay);
  padding: 2px 8px;
  border-radius: var(--radius-sm);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-base);
  color: var(--color-content-regular);
  line-height: 1;
}

.model-card__size {
  color: var(--color-content-regular);
  font-weight: var(--font-weight-medium);
  line-height: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .model-card__content {
    padding: 10px 12px 12px 12px;
  }
  
  .model-card__title,
  .model-card__meta,
  .model-card__file-type,
  .model-card__size {
    font-size: var(--font-size-base);
  }
}

/* 加载状态 */
.model-card--loading {
  pointer-events: none;
  opacity: 0.6;
}

.model-card--loading .model-card__image-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, var(--color-bg-overlay), transparent);
  animation: shimmer 1.5s infinite;
}

